"""
文件: llm_judge.py
功能: 使用大模型分析法律判决书内容并评估法院是否支持原告否认被告的法人人格
描述: 该脚本读取法律判决书数据，使用AI模型分析判决书内容，判断法院是否支持原告否认被告的法人人格。
      主要功能包括：
      1. 读取JSON格式的判决书样本数据
      2. 调用OpenRouter API进行文本分析（使用指定的AI模型）
      3. 处理和存储模型返回的分析结果
      4. 输出包含原始数据和模型分析结果的新JSON文件
      
      脚本设计为处理批量数据，并使用tqdm显示处理进度
"""
import os
import sys
from pathlib import Path
import pandas as pd
import json
import requests
import tiktoken
import time
import random
import re
from tqdm import tqdm
from openai import OpenAI
from prompt import system_judge_prompt


def responser(user_message, system_prompt, model_name):
    """
    调用OpenRouter API进行文本分析
    
    参数:
        user_message: 用户输入的文本内容
        system_prompt: 系统提示词，指导AI模型的回答方向
        model_name: 使用的AI模型名称
        
    返回:
        content: 模型返回的内容
        cost: 调用API的费用
    """
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key="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",
    )
    
    try:
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "http://localhost", # Optional. Site URL for rankings on openrouter.ai.
                "X-Title": "Context Analyzer", # Optional. Site title for rankings on openrouter.ai.
            },
            model=model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            # response_format={
            #     'type': 'json_object'
            # },
            temperature=0.2
        )

        # 从completion对象中获取内容
        content = completion.choices[0].message.content
        
        # 获取token使用情况
        usage = completion.usage
        prompt_tokens = usage.prompt_tokens
        completion_tokens = usage.completion_tokens
        
        # 计算费用 (基于每百万tokens的价格)
        # cost = (prompt_tokens / 1000000) * 0.25 + (completion_tokens / 1000000) * 0.85 #ds
        cost = (prompt_tokens / 1000000) * 0.4 + (completion_tokens / 1000000) * 1.6 #4.1
        # cost = (prompt_tokens / 1000000) * 0.15 + (completion_tokens / 1000000) * 0.60 #gm
        return content, cost

    except Exception as e:
        if "429" in str(e):
            print(f"速率限制超出 (429)，等待5秒后重试...")
            time.sleep(5)
            return responser(user_message, system_prompt, model_name)
        else:
            print(f"API调用错误: {e}")
            time.sleep(3)
            return responser(user_message, system_prompt, model_name)


def parse_llm_response(response):
    """
    解析大模型返回的结果，            # response_format={
            #     'type': 'json_object'
            # },
    处理可能被代码块标记```json ```包裹的响应
    
    参数:
        response: 大模型返回的响应文本
        
    返回:
        llm_result: 包含解析后的字段的字典
    """
    llm_result = {
        "是否存在被告滥用公司法人独立地位或股东有限责任": None,
        "滥用依据": None,
        "法院是否支持原告否认被告的法人人格": None,
        "否认人格依据": None
    }
    
    # 处理可能被代码块标记包裹的内容
    if response.startswith("```") and response.endswith("```"):
        response = response.strip("`")
        # 移除可能的语言标识符(如```json)
        lines = response.split("\n")
        if len(lines) > 1 and not lines[0].strip():
            response = "\n".join(lines[1:])
        elif len(lines) > 1 and lines[0].strip().lower() in ["json", "javascript"]:
            response = "\n".join(lines[1:])
    
    # 尝试解析JSON格式
    try:
        parsed_json = json.loads(response)
        
        # 提取四个字段
        if "是否存在被告滥用公司法人独立地位或股东有限责任" in parsed_json:
            llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"] = parsed_json["是否存在被告滥用公司法人独立地位或股东有限责任"]
        if "滥用依据" in parsed_json:
            llm_result["滥用依据"] = parsed_json["滥用依据"]
        if "法院是否支持原告否认被告的法人人格_llm" in parsed_json:  # 注意可能有_llm后缀
            llm_result["法院是否支持原告否认被告的法人人格"] = parsed_json["法院是否支持原告否认被告的法人人格_llm"]
        elif "法院是否支持原告否认被告的法人人格" in parsed_json:
            llm_result["法院是否支持原告否认被告的法人人格"] = parsed_json["法院是否支持原告否认被告的法人人格"]
        if "否认人格依据" in parsed_json:
            llm_result["否认人格依据"] = parsed_json["否认人格依据"]
            
        print("JSON格式解析成功")
        return llm_result
    except json.JSONDecodeError:
        print("JSON解析失败，尝试使用正则表达式")
        pass
    
    # 尝试使用正则表达式解析
    try:
        # 解析字段1：是否存在滥用
        abuse_match = re.search(r'"是否存在被告滥用公司法人独立地位或股东有限责任":\s*"([^"]+)"', response)
        if abuse_match:
            llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"] = abuse_match.group(1)
            
        # 解析字段2：滥用依据
        abuse_basis_match = re.search(r'"滥用依据":\s*"([^"]+)"', response)
        if abuse_basis_match:
            llm_result["滥用依据"] = abuse_basis_match.group(1)
        
        # 解析字段3："法院是否支持原告否认被告的法人人格"字段
        support_match = re.search(r'"法院是否支持原告否认被告的法人人格(?:_llm)?":\s*"([YN])"', response)
        if support_match:
            llm_result["法院是否支持原告否认被告的法人人格"] = support_match.group(1)
        
        # 解析字段4："否认人格依据"字段
        basis_match = re.search(r'"否认人格依据":\s*"([^"]+)"', response)
        if basis_match:
            llm_result["否认人格依据"] = basis_match.group(1)
            
        print("正则表达式解析成功")
        return llm_result
    except Exception as e:
        print(f"正则解析错误: {e}")
        return llm_result


def load_existing_case_numbers(output_file):
    """加载输出文件中已存在的案号集合"""
    existing_case_numbers = set()
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                for item in existing_data:
                    if "案号" in item:
                        existing_case_numbers.add(item["案号"])
            print(f"从输出文件中加载了 {len(existing_case_numbers)} 个已存在案号")
        except Exception as e:
            print(f"读取输出文件失败，将创建新文件: {e}")
    return existing_case_numbers


def append_to_json_file(file_path, new_data):
    """将新数据追加到JSON文件"""
    if os.path.exists(file_path):
        # 读取现有数据
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except:
            existing_data = []
        
        # 追加新数据
        existing_data.append(new_data)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
    else:
        # 文件不存在，创建新文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump([new_data], f, ensure_ascii=False, indent=2)


def append_to_failed_cases(file_path, failed_case):
    """将失败案例追加到失败案例文件"""
    # 如果文件不存在，创建新文件
    if not os.path.exists(file_path):
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump([failed_case], f, ensure_ascii=False, indent=2)
    else:
        # 读取现有数据
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except:
            existing_data = []
        
        # 追加新数据
        existing_data.append(failed_case)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    
    # 读取JSON数据文件
    input_file = '/home/<USER>/Xinwu/data_PCV.json'
    output_file = '/home/<USER>/Xinwu/4.1_full_judge.json'
    failed_cases_file = '/home/<USER>/Xinwu/4.1_failed_judge_cases.json'
    # model_name = "deepseek/deepseek-chat-v3-0324" 
    model_name = "openai/gpt-4.1-mini"  
    # model_name = "qwen/qwen3-235b-a22b"
    # model_name = "google/gemini-2.5-flash-preview-05-20"
    
    print(f"正在读取数据文件: {input_file}")
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载数据，共 {len(data)} 条记录")
    except Exception as e:
        print(f"读取数据文件失败: {e}")
        sys.exit(1)
    
    # 加载已存在的案号
    existing_case_numbers = load_existing_case_numbers(output_file)
    
    # 处理每条记录
    results = []
    total_cost = 0
    
    print(f"开始分析判决书内容...")
    for item in tqdm(data, desc="处理判决书"):
        case_number = item.get("案号", "")
        content = item.get("文书内容", "")
        procedure = item.get("审理程序", "")  # 获取审理程序
        
        # 跳过已存在的案号
        if case_number in existing_case_numbers:
            print(f"跳过已存在案号: {case_number}")
            continue
            
        # 实时输出当前正在处理的案号
        print(f"\n当前处理案号: {case_number}")
        
        if not content:
            print(f"警告: 案号 {case_number} 没有文书内容，记录为失败案例")
            failed_case = {
                "案号": case_number,
                "失败原因": "没有文书内容"
            }
            append_to_failed_cases(failed_cases_file, failed_case)
            continue
            
        try:
            # 构造输入内容，包含审理程序和文书内容
            combined_content = f"审理程序：{procedure}\n文书内容：{content}"
            
            # 调用模型分析判决书内容
            response, cost = responser(combined_content, system_judge_prompt, model_name)
            total_cost += cost
            
            # print(response)
            # 解析模型响应
            llm_result = parse_llm_response(response)
            
            # 检查结果是否有效
            is_valid = (llm_result["法院是否支持原告否认被告的法人人格"] is not None and
                       llm_result["否认人格依据"] is not None and
                       llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"] is not None and
                       llm_result["滥用依据"] is not None)
            
            if not is_valid:
                print(f"警告: 案例 {case_number} 解析结果不完整，记录为失败案例")
                failed_case = {
                    "案号": case_number,
                    "失败原因": "解析结果不完整",
                    "原始响应": response,
                    "解析结果": llm_result
                }
                append_to_failed_cases(failed_cases_file, failed_case)
                continue
            
            # 构造结果，不再包含文书内容和原始标注
            result = {
                "案号": case_number,
                "是否存在被告滥用公司法人独立地位或股东有限责任": llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"],
                "滥用依据": llm_result["滥用依据"],
                "法院是否支持原告否认被告的法人人格": llm_result["法院是否支持原告否认被告的法人人格"],
                "否认人格依据": llm_result["否认人格依据"]
            }
            
            # 打印当前案例的处理结果
            print(f"结果: 是否存在滥用={llm_result['是否存在被告滥用公司法人独立地位或股东有限责任']}, 是否支持否认={llm_result['法院是否支持原告否认被告的法人人格']}")
            
            # 将成功结果追加到输出文件
            append_to_json_file(output_file, result)
            results.append(result)
            
            # 更新已存在案号集合
            existing_case_numbers.add(case_number)
            
            # 休眠一小段时间避免请求过快
            # time.sleep(random.uniform(0.5, 1.5))
            
        except Exception as e:
            print(f"处理案例 {case_number} 时发生错误: {e}")
            failed_case = {
                "案号": case_number,
                "失败原因": str(e),
                "类型": "异常错误"
            }
            append_to_failed_cases(failed_cases_file, failed_case)
    
    # 打印统计信息
    print(f"分析结果已保存到: {output_file}")
    print(f"共成功处理 {len(results)} 个案例")
    print(f"总API费用估计: ${total_cost:.6f}")
    
    # 检查失败案例文件
    if os.path.exists(failed_cases_file):
        with open(failed_cases_file, 'r', encoding='utf-8') as f:
            failed_cases = json.load(f)
        print(f"处理失败的案例已保存到: {failed_cases_file}")
        print(f"共有 {len(failed_cases)} 个案例处理失败")